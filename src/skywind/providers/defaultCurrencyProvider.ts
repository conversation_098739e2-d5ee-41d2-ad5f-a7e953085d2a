import { CurrencyProvider, ExchangeRate, ExchangeRateProvider, ExchangeRateType } from "../types";
import { getEndTime, getStartTime, getTimestamp } from "../utils";

const currencyResponses = require("./default_currency_rates.json");

export class DefaultCurrencyProvider implements CurrencyProvider {

    public async getRates(date: Date, baseCurrencies: string[], type = ExchangeRateType.BID): Promise<ExchangeRate> {
        return {
            provider: ExchangeRateProvider.DEFAULT,
            type,
            ts: getTimestamp(date),
            startTime: getStartTime(date),
            endTime: getEndTime(date),
            rates: {
                [currencyResponses.base]: currencyResponses.rates
            }
        };
    }
}
