{"AED": {"name": "UAE Dirham", "iso": {"code": "AED", "number": "784", "minorUnits": 2}}, "AFN": {"name": "Afghani", "iso": {"code": "AFN", "number": "971", "minorUnits": 2}}, "ALL": {"name": "Lek", "iso": {"code": "ALL", "number": "008", "minorUnits": 2}}, "AMD": {"name": "Armenian Dram", "iso": {"code": "AMD", "number": "051", "minorUnits": 2}, "toEURMultiplier": 500}, "ANG": {"name": "Netherlands Antillean Guilder", "iso": {"code": "ANG", "number": "532", "minorUnits": 2}}, "AOA": {"name": "Kwan<PERSON>", "iso": {"code": "AOA", "number": "973", "minorUnits": 2}, "copyLimitsFrom": "CLP", "toEURMultiplier": 500}, "ARS": {"name": "Argentine Peso", "iso": {"code": "ARS", "number": "032", "minorUnits": 2}, "toEURMultiplier": 10}, "AUD": {"name": "Australian Dollar", "iso": {"code": "AUD", "number": "036", "minorUnits": 2}, "toEURMultiplier": 1}, "AWG": {"name": "Aruban Florin", "iso": {"code": "AWG", "number": "533", "minorUnits": 2}}, "AZN": {"name": "<PERSON><PERSON>", "iso": {"code": "AZN", "number": "944", "minorUnits": 2}, "toEURMultiplier": 1}, "BAM": {"name": "Convertible Mark", "iso": {"code": "BAM", "number": "977", "minorUnits": 2}}, "BBD": {"name": "Barbados Dollar", "iso": {"code": "BBD", "number": "052", "minorUnits": 2}}, "BDT": {"name": "<PERSON><PERSON>", "iso": {"code": "BDT", "number": "050", "minorUnits": 2}}, "BGN": {"name": "Bulgarian Lev", "iso": {"code": "BGN", "number": "975", "minorUnits": 2}, "toEURMultiplier": 1}, "BHD": {"name": "<PERSON><PERSON>", "iso": {"code": "BHD", "number": "048", "minorUnits": 3}}, "BIF": {"name": "Burundi Franc", "iso": {"code": "BIF", "number": "108", "minorUnits": 0}}, "BMD": {"name": "Bermudian Dollar", "iso": {"code": "BMD", "number": "060", "minorUnits": 2}, "toEURMultiplier": 1}, "BND": {"name": "Brunei Dollar", "iso": {"code": "BND", "number": "096", "minorUnits": 2}, "toEURMultiplier": 1}, "BOB": {"name": "Boliviano", "iso": {"code": "BOB", "number": "068", "minorUnits": 2}}, "BOV": {"name": "Mvdol", "iso": {"code": "BOV", "number": "984", "minorUnits": 2}}, "BRL": {"name": "Brazilian Real", "iso": {"code": "BRL", "number": "986", "minorUnits": 2}, "toEURMultiplier": 5}, "BSD": {"name": "Bahamian Dollar", "iso": {"code": "BSD", "number": "044", "minorUnits": 2}}, "UBT": {"name": "Micro Bitcoin (bit)", "iso": {"code": "UBT", "number": "999", "minorUnits": 2}}, "MBT": {"name": "Milli Bitcoin", "iso": {"code": "MBT", "number": "999", "minorUnits": 5}}, "BTN": {"name": "Ngultrum", "iso": {"code": "BTN", "number": "064", "minorUnits": 2}}, "BWP": {"name": "<PERSON><PERSON>", "iso": {"code": "BWP", "number": "072", "minorUnits": 2}}, "BYN": {"name": "Belarusian Ruble", "iso": {"code": "BYN", "number": "933", "minorUnits": 2}}, "BYR": {"name": "Belarusian Ruble", "iso": {"code": "BYR", "number": "974", "minorUnits": 0}}, "BZD": {"name": "Belize Dollar", "iso": {"code": "BZD", "number": "084", "minorUnits": 2}}, "CAD": {"name": "Canadian Dollar", "iso": {"code": "CAD", "number": "124", "minorUnits": 2}, "toEURMultiplier": 1}, "CDF": {"name": "Congolese Franc", "iso": {"code": "CDF", "number": "976", "minorUnits": 2}, "toEURMultiplier": 1000}, "CHE": {"name": "WIR Euro", "iso": {"code": "CHE", "number": "947", "minorUnits": 2}}, "CHF": {"name": "Swiss Franc", "iso": {"code": "CHF", "number": "756", "minorUnits": 2}, "toEURMultiplier": 1}, "CHW": {"name": "WIR Franc", "iso": {"code": "CHW", "number": "948", "minorUnits": 2}}, "CLF": {"name": "Unidad de Fomento", "iso": {"code": "CLF", "number": "990", "minorUnits": 4}}, "CLP": {"name": "Chilean Peso", "iso": {"code": "CLP", "number": "152", "minorUnits": 0}, "toEURMultiplier": 500}, "CNY": {"name": "<PERSON>", "iso": {"code": "CNY", "number": "156", "minorUnits": 2}, "toEURMultiplier": 10}, "COP": {"name": "Colombian Peso", "iso": {"code": "COP", "number": "170", "minorUnits": 2}, "toEURMultiplier": 1000}, "COU": {"name": "Unidad de Valor Real", "iso": {"code": "COU", "number": "970", "minorUnits": 2}}, "CRC": {"name": "Costa Rican Colon", "iso": {"code": "CRC", "number": "188", "minorUnits": 2}, "toEURMultiplier": 500}, "CUC": {"name": "Peso Convertible", "iso": {"code": "CUC", "number": "931", "minorUnits": 2}}, "CUP": {"name": "Cuban Peso", "iso": {"code": "CUP", "number": "192", "minorUnits": 2}}, "CVE": {"name": "Cabo Verde Escudo", "iso": {"code": "CVE", "number": "132", "minorUnits": 2}}, "CZK": {"name": "Czech Koruna", "iso": {"code": "CZK", "number": "203", "minorUnits": 2}, "toEURMultiplier": 50}, "DJF": {"name": "Djibouti Franc", "iso": {"code": "DJF", "number": "262", "minorUnits": 0}}, "DKK": {"name": "Danish Krone", "iso": {"code": "DKK", "number": "208", "minorUnits": 2}, "toEURMultiplier": 10}, "DOP": {"name": "Dominican Peso", "iso": {"code": "DOP", "number": "214", "minorUnits": 2}, "toEURMultiplier": 50}, "DZD": {"name": "Algerian Dinar", "iso": {"code": "DZD", "number": "012", "minorUnits": 2}}, "EGP": {"name": "Egyptian Pound", "iso": {"code": "EGP", "number": "818", "minorUnits": 2}}, "ERN": {"name": "Nakfa", "iso": {"code": "ERN", "number": "232", "minorUnits": 2}}, "ETB": {"name": "Ethiopian Birr", "iso": {"code": "ETB", "number": "230", "minorUnits": 2}}, "EUR": {"name": "Euro", "iso": {"code": "EUR", "number": "978", "minorUnits": 2}, "toEURMultiplier": 1}, "FJD": {"name": "Fiji Dollar", "iso": {"code": "FJD", "number": "242", "minorUnits": 2}}, "FKP": {"name": "Falkland Islands Pound", "iso": {"code": "FKP", "number": "238", "minorUnits": 2}}, "GBP": {"name": "Pound Sterling", "iso": {"code": "GBP", "number": "826", "minorUnits": 2}, "toEURMultiplier": 1}, "GEL": {"name": "<PERSON><PERSON>", "iso": {"code": "GEL", "number": "981", "minorUnits": 2}, "toEURMultiplier": 1}, "GHS": {"name": "Ghana Cedi", "iso": {"code": "GHS", "number": "936", "minorUnits": 2}, "toEURMultiplier": 5}, "GIP": {"name": "Gibraltar Pound", "iso": {"code": "GIP", "number": "292", "minorUnits": 2}}, "GMD": {"name": "<PERSON><PERSON>", "iso": {"code": "GMD", "number": "270", "minorUnits": 2}}, "GNF": {"name": "Guinea Franc", "iso": {"code": "GNF", "number": "324", "minorUnits": 0}, "copyLimitsFrom": "IDR", "toEURMultiplier": 10000}, "GTQ": {"name": "Quetzal", "iso": {"code": "GTQ", "number": "320", "minorUnits": 2}}, "GYD": {"name": "Guyana Dollar", "iso": {"code": "GYD", "number": "328", "minorUnits": 2}}, "HKD": {"name": "Hong Kong Dollar", "iso": {"code": "HKD", "number": "344", "minorUnits": 2}, "toEURMultiplier": 10}, "HNL": {"name": "<PERSON><PERSON><PERSON>", "iso": {"code": "HNL", "number": "340", "minorUnits": 2}, "toEURMultiplier": 50}, "HRK": {"name": "<PERSON><PERSON>", "iso": {"code": "HRK", "number": "191", "minorUnits": 2}, "toEURMultiplier": 10}, "HTG": {"name": "<PERSON><PERSON><PERSON>", "iso": {"code": "HTG", "number": "332", "minorUnits": 2}}, "HUF": {"name": "Forint", "iso": {"code": "HUF", "number": "348", "minorUnits": 2}, "toEURMultiplier": 500}, "IDR": {"name": "<PERSON><PERSON><PERSON>", "iso": {"code": "IDR", "number": "360", "minorUnits": 2}, "toEURMultiplier": 10000}, "IDS": {"name": "<PERSON><PERSON><PERSON>", "description": "Artificial short version of IDR currency", "iso": {"code": "IDS", "number": "360", "minorUnits": 3}, "toEURMultiplier": 10}, "RUP": {"name": "<PERSON><PERSON><PERSON>", "description": "Artificial short version of IDR currency", "iso": {"code": "RUP", "number": "360", "minorUnits": 3}, "toEURMultiplier": 10}, "ILS": {"name": "New Israeli Sheqel", "iso": {"code": "ILS", "number": "376", "minorUnits": 2}, "toEURMultiplier": 5}, "INR": {"name": "Indian Rupee", "iso": {"code": "INR", "number": "356", "minorUnits": 2}, "toEURMultiplier": 50}, "IQD": {"name": "Iraqi <PERSON>", "iso": {"code": "IQD", "number": "368", "minorUnits": 3}}, "IRR": {"name": "Iranian Rial", "iso": {"code": "IRR", "number": "364", "minorUnits": 2}}, "ISK": {"name": "Iceland Krona", "iso": {"code": "ISK", "number": "352", "minorUnits": 0}, "toEURMultiplier": 100}, "JMD": {"name": "Jamaican Dollar", "iso": {"code": "JMD", "number": "388", "minorUnits": 2}}, "JOD": {"name": "<PERSON><PERSON>", "iso": {"code": "JOD", "number": "400", "minorUnits": 3}}, "JPY": {"name": "Yen", "iso": {"code": "JPY", "number": "392", "minorUnits": 0}, "toEURMultiplier": 100}, "KES": {"name": "Kenyan Shilling", "iso": {"code": "KES", "number": "404", "minorUnits": 2}, "toEURMultiplier": 100}, "KGS": {"name": "Som", "iso": {"code": "KGS", "number": "417", "minorUnits": 2}, "toEURMultiplier": 50}, "KHR": {"name": "Riel", "iso": {"code": "KHR", "number": "116", "minorUnits": 2}}, "KMF": {"name": "<PERSON><PERSON>", "iso": {"code": "KMF", "number": "174", "minorUnits": 0}}, "KPW": {"name": "North Korean Won", "iso": {"code": "KPW", "number": "408", "minorUnits": 2}}, "KRW": {"name": "Won", "iso": {"code": "KRW", "number": "410", "minorUnits": 0}, "toEURMultiplier": 1000}, "KWD": {"name": "<PERSON><PERSON>", "iso": {"code": "KWD", "number": "414", "minorUnits": 3}}, "KYD": {"name": "Cayman Islands Dollar", "iso": {"code": "KYD", "number": "136", "minorUnits": 2}}, "KZT": {"name": "Tenge", "iso": {"code": "KZT", "number": "398", "minorUnits": 2}, "toEURMultiplier": 500}, "LAK": {"name": "<PERSON><PERSON>", "iso": {"code": "LAK", "number": "418", "minorUnits": 2}}, "LBP": {"name": "Lebanese Pound", "iso": {"code": "LBP", "number": "422", "minorUnits": 2}}, "LKR": {"name": "Sri Lanka Rupee", "iso": {"code": "LKR", "number": "144", "minorUnits": 2}}, "LRD": {"name": "Liberian Dollar", "iso": {"code": "LRD", "number": "430", "minorUnits": 2}, "toEURMultiplier": 100}, "LSL": {"name": "Loti", "iso": {"code": "LSL", "number": "426", "minorUnits": 2}}, "LYD": {"name": "Libyan Dinar", "iso": {"code": "LYD", "number": "434", "minorUnits": 3}}, "MAD": {"name": "Moroccan <PERSON><PERSON><PERSON>", "iso": {"code": "MAD", "number": "504", "minorUnits": 2}, "toEURMultiplier": 10}, "MDL": {"name": "Moldovan Leu", "iso": {"code": "MDL", "number": "498", "minorUnits": 2}, "toEURMultiplier": 50}, "MGA": {"name": "Malagasy Ariary", "iso": {"code": "MGA", "number": "969", "minorUnits": 2}}, "MKD": {"name": "<PERSON><PERSON>", "iso": {"code": "MKD", "number": "807", "minorUnits": 2}}, "MMK": {"name": "Kyat", "iso": {"code": "MMK", "number": "104", "minorUnits": 2}, "toEURMultiplier": 1000}, "MNT": {"name": "<PERSON><PERSON><PERSON>", "iso": {"code": "MNT", "number": "496", "minorUnits": 2}, "toEURMultiplier": 1000}, "MOP": {"name": "Pataca", "iso": {"code": "MOP", "number": "446", "minorUnits": 2}, "toEURMultiplier": 10}, "MRO": {"name": "Ouguiya", "iso": {"code": "MRO", "number": "478", "minorUnits": 2}}, "MRU": {"name": "Ouguiya", "iso": {"code": "MRU", "number": "929", "minorUnits": 2}}, "MUR": {"name": "Mauritius Rupee", "iso": {"code": "MUR", "number": "480", "minorUnits": 2}}, "MVR": {"name": "<PERSON><PERSON><PERSON><PERSON>", "iso": {"code": "MVR", "number": "462", "minorUnits": 2}}, "MWK": {"name": "Malawi Kwacha", "iso": {"code": "MWK", "number": "454", "minorUnits": 2}, "toEURMultiplier": 1000}, "MXN": {"name": "Mexican Peso", "iso": {"code": "MXN", "number": "484", "minorUnits": 2}, "toEURMultiplier": 30}, "MXV": {"name": "Mexican Unidad de Inversion (UDI)", "iso": {"code": "MXV", "number": "979", "minorUnits": 2}}, "MYR": {"name": "Malaysian Ringgit", "iso": {"code": "MYR", "number": "458", "minorUnits": 2}, "toEURMultiplier": 5}, "MZN": {"name": "Mozambique Metical", "iso": {"code": "MZN", "number": "943", "minorUnits": 2}, "copyLimitsFrom": "ISK", "toEURMultiplier": 100}, "NAD": {"name": "Namibia Dollar", "iso": {"code": "NAD", "number": "516", "minorUnits": 2}}, "NGN": {"name": "<PERSON><PERSON>", "iso": {"code": "NGN", "number": "566", "minorUnits": 2}, "toEURMultiplier": 500}, "NIO": {"name": "Cordoba Oro", "iso": {"code": "NIO", "number": "558", "minorUnits": 2}, "toEURMultiplier": 50}, "NOK": {"name": "Norwegian Krone", "iso": {"code": "NOK", "number": "578", "minorUnits": 2}, "toEURMultiplier": 10}, "NPR": {"name": "Nepalese Rupee", "iso": {"code": "NPR", "number": "524", "minorUnits": 2}}, "NZD": {"name": "New Zealand Dollar", "iso": {"code": "NZD", "number": "554", "minorUnits": 2}, "toEURMultiplier": 1}, "OMR": {"name": "<PERSON><PERSON>", "iso": {"code": "OMR", "number": "512", "minorUnits": 3}}, "PAB": {"name": "Balboa", "iso": {"code": "PAB", "number": "590", "minorUnits": 2}}, "PEN": {"name": "Sol", "iso": {"code": "PEN", "number": "604", "minorUnits": 2}, "toEURMultiplier": 5}, "PGK": {"name": "<PERSON><PERSON>", "iso": {"code": "PGK", "number": "598", "minorUnits": 2}}, "PHP": {"name": "Philippine Peso", "iso": {"code": "PHP", "number": "608", "minorUnits": 2}, "toEURMultiplier": 50}, "PKR": {"name": "Pakistan Rupee", "iso": {"code": "PKR", "number": "586", "minorUnits": 2}}, "PLN": {"name": "<PERSON><PERSON><PERSON>", "iso": {"code": "PLN", "number": "985", "minorUnits": 2}, "toEURMultiplier": 5}, "PYG": {"name": "Guarani", "iso": {"code": "PYG", "number": "600", "minorUnits": 0}, "toEURMultiplier": 5000}, "QAR": {"name": "<PERSON><PERSON> R<PERSON>", "iso": {"code": "QAR", "number": "634", "minorUnits": 2}}, "RON": {"name": "Romanian Leu", "iso": {"code": "RON", "number": "946", "minorUnits": 2}, "toEURMultiplier": 5}, "RSD": {"name": "Serbian Dinar", "iso": {"code": "RSD", "number": "941", "minorUnits": 2}, "toEURMultiplier": 100}, "RUB": {"name": "Russian Ruble", "iso": {"code": "RUB", "number": "643", "minorUnits": 2}, "toEURMultiplier": 50}, "RWF": {"name": "Rwanda Franc", "iso": {"code": "RWF", "number": "646", "minorUnits": 0}, "toEURMultiplier": 1000}, "SAR": {"name": "Saudi Riyal", "iso": {"code": "SAR", "number": "682", "minorUnits": 2}}, "SBD": {"name": "Solomon Islands Dollar", "iso": {"code": "SBD", "number": "090", "minorUnits": 2}}, "SCR": {"name": "Seychelles Rupee", "iso": {"code": "SCR", "number": "690", "minorUnits": 2}}, "SDG": {"name": "Sudanese Pound", "iso": {"code": "SDG", "number": "938", "minorUnits": 2}}, "SEK": {"name": "Swedish Krona", "iso": {"code": "SEK", "number": "752", "minorUnits": 2}, "toEURMultiplier": 10}, "SGD": {"name": "Singapore Dollar", "iso": {"code": "SGD", "number": "702", "minorUnits": 2}, "toEURMultiplier": 1}, "SHP": {"name": "<PERSON>", "iso": {"code": "SHP", "number": "654", "minorUnits": 2}}, "SLE": {"name": "Leone", "iso": {"code": "SLE", "number": "925", "minorUnits": 2}, "toEURMultiplier": 10}, "SLL": {"name": "Leone", "iso": {"code": "SLL", "number": "694", "minorUnits": 2}, "copyLimitsFrom": "IDR", "toEURMultiplier": 10000}, "SOS": {"name": "Somali Shilling", "iso": {"code": "SOS", "number": "706", "minorUnits": 2}}, "SRD": {"name": "Surinam Dollar", "iso": {"code": "SRD", "number": "968", "minorUnits": 2}}, "SSP": {"name": "South Sudanese Pound", "iso": {"code": "SSP", "number": "728", "minorUnits": 2}}, "STD": {"name": "Dobra", "iso": {"code": "STD", "number": "678", "minorUnits": 2}}, "STN": {"name": "Dobra", "iso": {"code": "STN", "number": "930", "minorUnits": 2}}, "SVC": {"name": "El Salvador Colon", "iso": {"code": "SVC", "number": "222", "minorUnits": 2}}, "SYP": {"name": "Syrian Pound", "iso": {"code": "SYP", "number": "760", "minorUnits": 2}}, "SZL": {"name": "<PERSON><PERSON><PERSON>", "iso": {"code": "SZL", "number": "748", "minorUnits": 2}}, "THB": {"name": "Baht", "iso": {"code": "THB", "number": "764", "minorUnits": 2}, "toEURMultiplier": 50}, "TJS": {"name": "So<PERSON><PERSON>", "iso": {"code": "TJS", "number": "972", "minorUnits": 2}}, "TMT": {"name": "Turkmenistan New Manat", "iso": {"code": "TMT", "number": "934", "minorUnits": 2}}, "TND": {"name": "Tunisian Dinar", "iso": {"code": "TND", "number": "788", "minorUnits": 3}, "copyLimitsFrom": "ILS", "toEURMultiplier": 5}, "TOP": {"name": "Pa’anga", "iso": {"code": "TOP", "number": "776", "minorUnits": 2}}, "TRY": {"name": "Turkish Lira", "iso": {"code": "TRY", "number": "949", "minorUnits": 2}, "toEURMultiplier": 5}, "TTD": {"name": "Trinidad and Tobago Dollar", "iso": {"code": "TTD", "number": "780", "minorUnits": 2}}, "TWD": {"name": "New Taiwan Dollar", "iso": {"code": "TWD", "number": "901", "minorUnits": 2}, "toEURMultiplier": 50}, "TZS": {"name": "Tanzanian <PERSON>", "iso": {"code": "TZS", "number": "834", "minorUnits": 2}, "toEURMultiplier": 1000}, "UAH": {"name": "Hryvnia", "iso": {"code": "UAH", "number": "980", "minorUnits": 2}, "toEURMultiplier": 50}, "UGX": {"name": "Uganda Shilling", "iso": {"code": "UGX", "number": "800", "minorUnits": 0}, "toEURMultiplier": 5000}, "USD": {"name": "US Dollar", "iso": {"code": "USD", "number": "840", "minorUnits": 2}, "toEURMultiplier": 1, "copyLimitsFrom": "EUR"}, "USN": {"name": "US Dollar (Next day)", "iso": {"code": "USN", "number": "997", "minorUnits": 2}}, "UYI": {"name": "Uruguay Peso en Unidades Indexadas (URUIURUI)", "iso": {"code": "UYI", "number": "940", "minorUnits": 0}}, "UYU": {"name": "Peso Uruguayo", "iso": {"code": "UYU", "number": "858", "minorUnits": 2}, "toEURMultiplier": 50}, "UYW": {"name": "Unidad Previsional", "iso": {"code": "UYW", "number": "927", "minorUnits": 4}}, "UZS": {"name": "Uzbekistan Sum", "iso": {"code": "UZS", "number": "860", "minorUnits": 2}}, "VEF": {"name": "Bolívar", "iso": {"code": "VEF", "number": "937", "minorUnits": 2}, "toEURMultiplier": 10}, "VES": {"name": "<PERSON><PERSON><PERSON><PERSON>", "iso": {"code": "VES", "number": "928", "minorUnits": 2}, "toEURMultiplier": 50}, "VND": {"name": "<PERSON>", "iso": {"code": "VND", "number": "704", "minorUnits": 0}, "toEURMultiplier": 25000}, "VNS": {"name": "<PERSON>", "description": "Artificial short version of VND currency", "iso": {"code": "VNS", "number": "704", "minorUnits": 3}, "toEURMultiplier": 25}, "VDO": {"name": "<PERSON>", "description": "Artificial short version of VND currency", "iso": {"code": "VDO", "number": "704", "minorUnits": 3}, "toEURMultiplier": 25}, "VUV": {"name": "Vatu", "iso": {"code": "VUV", "number": "548", "minorUnits": 0}}, "WST": {"name": "<PERSON><PERSON>", "iso": {"code": "WST", "number": "882", "minorUnits": 2}}, "XAF": {"name": "CFA Franc BEAC", "iso": {"code": "XAF", "number": "950", "minorUnits": 0}, "toEURMultiplier": 500}, "XAU": {"name": "Gold", "iso": {"code": "XAU", "number": "959", "minorUnits": 0}}, "XPD": {"name": "Palladium", "iso": {"code": "XPD", "number": "964", "minorUnits": 0}}, "XPT": {"name": "Platinum", "iso": {"code": "XPT", "number": "962", "minorUnits": 0}}, "XAG": {"name": "Silver", "iso": {"code": "XAG", "number": "961", "minorUnits": 0}}, "XBA": {"name": "Bond Markets Unit European Composite Unit (EURCO)", "iso": {"code": "XBA", "number": "955", "minorUnits": 0}}, "XBB": {"name": "Bond Markets Unit European Monetary Unit (E.M.U.-6)", "iso": {"code": "XBB", "number": "956", "minorUnits": 0}}, "XBC": {"name": "Bond Markets Unit European Unit of Account 9 (E.U.A.-9)", "iso": {"code": "XBC", "number": "957", "minorUnits": 0}}, "XBD": {"name": "Bond Markets Unit European Unit of Account 17 (E.U.A.-17)", "iso": {"code": "XBD", "number": "958", "minorUnits": 0}}, "XCD": {"name": "East Caribbean Dollar", "iso": {"code": "XCD", "number": "951", "minorUnits": 2}}, "XDR": {"name": "SDR (Special Drawing Right)", "iso": {"code": "XDR", "number": "960", "minorUnits": 0}}, "XOF": {"name": "CFA Franc BCEAO", "iso": {"code": "XOF", "number": "952", "minorUnits": 0}, "toEURMultiplier": 500}, "XPF": {"name": "CFP Franc", "iso": {"code": "XPF", "number": "953", "minorUnits": 0}}, "XSU": {"name": "<PERSON><PERSON>", "iso": {"code": "XSU", "number": "994", "minorUnits": 0}}, "XTS": {"name": "Codes specifically reserved for testing purposes", "iso": {"code": "XTS", "number": "963", "minorUnits": 0}}, "XUA": {"name": "ADB Unit of Account", "iso": {"code": "XUA", "number": "965", "minorUnits": 0}}, "XXX": {"name": "The codes assigned for transactions where no currency is involved", "iso": {"code": "XXX", "number": "999", "minorUnits": 2}, "type": "virtual", "toEURMultiplier": 1}, "FUN": {"name": "Currency for FUN mode", "iso": {"code": "FUN", "number": "999", "minorUnits": 2}, "type": "virtual", "copyLimitsFrom": "XXX", "toEURMultiplier": 1}, "YER": {"name": "Yemeni R<PERSON>", "iso": {"code": "YER", "number": "886", "minorUnits": 2}}, "ZAR": {"name": "Rand", "iso": {"code": "ZAR", "number": "710", "minorUnits": 2}, "toEURMultiplier": 10}, "ZMW": {"name": "Zambian <PERSON>", "iso": {"code": "ZMW", "number": "967", "minorUnits": 2}, "toEURMultiplier": 10}, "ZWL": {"name": "Zimbabwe Dollar", "iso": {"code": "ZWL", "number": "932", "minorUnits": 2}}, "BNS": {"name": "Bonus Coin", "iso": {"code": "BNS", "number": "999", "minorUnits": 2}, "type": "virtual", "toEURMultiplier": 1000}, "WSC": {"name": "Virtual currency for Social games in Falcon side.", "iso": {"code": "WSC", "number": "999", "minorUnits": 2}, "toEURMultiplier": 1}, "MXX": {"name": "Artificial currency for Casino770 - Mexican Peso", "iso": {"code": "MXX", "number": "999", "minorUnits": 2}, "toEURMultiplier": 3000}, "USX": {"name": "Artificial currency for Casino770 - US Dollar", "iso": {"code": "USX", "number": "999", "minorUnits": 2}, "toEURMultiplier": 100}, "CDX": {"name": "Artificial currency for Casino770 - Canadian Dollar", "iso": {"code": "CDX", "number": "999", "minorUnits": 2}, "toEURMultiplier": 100}, "GBX": {"name": "Artificial currency for Casino770 - Pound Sterling", "iso": {"code": "GBX", "number": "999", "minorUnits": 2}, "toEURMultiplier": 100}, "EUX": {"name": "Artificial currency for Casino770 - Euro", "iso": {"code": "EUX", "number": "999", "minorUnits": 2}, "toEURMultiplier": 100}, "MEH": {"name": "Milli Ethereum", "iso": {"code": "MEH", "number": "999", "minorUnits": 6}}, "MLC": {"name": "<PERSON><PERSON>", "iso": {"code": "MLC", "number": "999", "minorUnits": 5}}, "MCH": {"name": "Milli Bitcoin Cash", "iso": {"code": "MCH", "number": "999", "minorUnits": 5}, "provider": "oxr"}, "DOG": {"name": "<PERSON><PERSON><PERSON><PERSON>", "iso": {"code": "DOG", "number": "999", "minorUnits": 0}, "provider": "oxr", "clientMinorUnits": 2, "clientMoneyFormat": {"code": "DOGE"}}, "TET": {"name": "Tether (USDT)", "iso": {"code": "TET", "number": "999", "minorUnits": 0}, "clientMinorUnits": 2, "clientMoneyFormat": {"code": "USDT"}}, "BTC": {"name": "Bitcoin", "iso": {"code": "BTC", "number": "999", "minorUnits": 0}, "clientMinorUnits": 2, "clientMoneyFormat": {"code": "UBTC"}}, "ETH": {"name": "Ethereum", "iso": {"code": "ETH", "number": "999", "minorUnits": 0}, "provider": "oxr", "clientMinorUnits": 2, "clientMoneyFormat": {"code": "METH"}}, "LTC": {"name": "Litecoin", "iso": {"code": "LTC", "number": "999", "minorUnits": 0}, "provider": "oxr", "clientMinorUnits": 2, "clientMoneyFormat": {"code": "MLTC"}}, "XGC": {"name": "Crown Coins", "iso": {"code": "XGC", "number": "999", "minorUnits": 0}, "type": "social", "toEURMultiplier": 20000, "disableGGR": true}, "XSC": {"name": "Sweepstakes Coin", "iso": {"code": "XSC", "number": "999", "minorUnits": 2}, "type": "social", "toEURMultiplier": 1}, "FCO": {"name": "FC", "iso": {"code": "FCO", "number": "999", "minorUnits": 2}, "type": "social", "toEURMultiplier": 1}, "GCO": {"name": "GC", "iso": {"code": "GCO", "number": "999", "minorUnits": 0}, "type": "social", "toEURMultiplier": 10000, "disableGGR": true}, "X01": {"name": "<PERSON>", "iso": {"code": "X01", "number": "999", "minorUnits": 2}, "type": "social", "toEURMultiplier": 200}, "X02": {"name": "Bit", "iso": {"code": "X02", "number": "999", "minorUnits": 2}, "type": "social", "toEURMultiplier": 200}, "X03": {"name": "USD Coin (USDC)", "iso": {"code": "X03", "number": "999", "minorUnits": 0}, "toEURMultiplier": 1000000, "clientMinorUnits": 2, "clientMoneyFormat": {"code": "USDC"}}, "X04": {"name": "Gold Coin", "iso": {"code": "X04", "number": "999", "minorUnits": 0}, "type": "social", "toEURMultiplier": 200, "disableGGR": true}, "X05": {"name": "Gold Coin", "iso": {"code": "X05", "number": "999", "minorUnits": 0}, "type": "social", "toEURMultiplier": 5000, "disableGGR": true}, "X06": {"name": "Gold Coin", "iso": {"code": "X06", "number": "999", "minorUnits": 2}, "type": "social", "toEURMultiplier": 2000, "disableGGR": true}, "X07": {"name": "Fortune Coin", "iso": {"code": "X07", "number": "999", "minorUnits": 0}, "type": "social", "toEURMultiplier": 100}, "X08": {"name": "COIN", "iso": {"code": "X08", "number": "999", "minorUnits": 2}, "type": "social", "toEURMultiplier": 200}, "X09": {"name": "RSC", "iso": {"code": "X09", "number": "999", "minorUnits": 2}, "type": "social", "toEURMultiplier": 1}, "X10": {"name": "RGC", "iso": {"code": "X10", "number": "999", "minorUnits": 2}, "type": "social", "toEURMultiplier": 500, "disableGGR": true}, "ARB": {"name": "Dollar Blue", "iso": {"code": "ARB", "number": "999", "minorUnits": 2}, "toEURMultiplier": 1000, "provider": "argentinadatos"}, "VBC": {"name": "VBC", "iso": {"code": "VBC", "number": "999", "minorUnits": 2}, "type": "social", "toEURMultiplier": 1, "disableGGR": true}, "WOC": {"name": "WOC", "iso": {"code": "WOC", "number": "999", "minorUnits": 2}, "type": "social", "toEURMultiplier": 1}, "CON": {"name": "Coin", "iso": {"code": "CON", "number": "999", "minorUnits": 2}, "type": "social", "toEURMultiplier": 1}, "X11": {"name": "Gold Coins", "iso": {"code": "X11", "number": "999", "minorUnits": 0}, "type": "social", "toEURMultiplier": 10000, "disableGGR": true}, "X12": {"name": "Sweepstakes Coin", "iso": {"code": "X12", "number": "999", "minorUnits": 2}, "type": "social", "toEURMultiplier": 1}, "X13": {"name": "Gold Coins", "iso": {"code": "X13", "number": "999", "minorUnits": 2}, "type": "social", "toEURMultiplier": 500, "disableGGR": true}, "X14": {"name": "Gold Coins", "iso": {"code": "X14", "number": "999", "minorUnits": 2}, "type": "social", "toEURMultiplier": 100, "disableGGR": true}, "FBC": {"name": "Free Bet Coin", "description": "The code assigned for free bet transactions", "iso": {"code": "FBC", "number": "999", "minorUnits": 2}, "toEURMultiplier": 1, "disableGGR": true}, "BNC": {"name": "Bonus Coin", "iso": {"code": "BNC", "number": "999", "minorUnits": 2}, "toEURMultiplier": 1, "disableGGR": true, "funBonus": true}}