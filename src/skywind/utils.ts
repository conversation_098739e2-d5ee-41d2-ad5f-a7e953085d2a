import * as jwt from "jsonwebtoken";

export function getTimestamp(date: Date): string {
    return date.toISOString().substr(0, 10);
}

export function getPrevDay(date = new Date(), days = 1): Date {
    const prevDay = new Date(date);
    prevDay.setUTCDate(prevDay.getUTCDate() - days);
    prevDay.setUTCHours(0, 0, 0, 0);
    return prevDay;
}

export function getNextDay(date = new Date(), days = 1): Date {
    const nextDay = new Date(date);
    nextDay.setUTCDate(nextDay.getUTCDate() + days);
    nextDay.setUTCHours(0, 0, 0, 0);
    return nextDay;
}

export function getStartTime(date: Date): number {
    const dayStart = new Date(date);
    dayStart.setUTCHours(0, 0, 0, 0);
    return dayStart.getTime();
}

export function getEndTime(date: Date): number {
    const dayEnd = new Date(date);
    dayEnd.setUTCDate(date.getUTCDate() + 1);
    dayEnd.setUTCHours(0, 0, 0, 0);
    return dayEnd.getTime() - 1;
}

export function generateToken(tokenConfig): Promise<string> {
    const cfg = tokenConfig;
    return new Promise<string>((resolve, reject) => {
        jwt.sign({}, cfg.secret, {
            algorithm: cfg.algorithm,
            expiresIn: cfg.expiresIn,
            issuer: cfg.issuer,
        }, (err, token) => {
            return err ? reject(err) : resolve(token);
        });
    });
}
