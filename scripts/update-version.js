const {readFileSync, writeFileSync, readdirSync, statSync} = require("node:fs");
const path = require("node:path");

function findPackageJsonFiles(dir = ".") {
    const files = [];

    function searchDirectory(currentDir) {
        try {
            const entries = readdirSync(currentDir);

            for (const entry of entries) {
                const fullPath = path.join(currentDir, entry);
                const stat = statSync(fullPath);

                if (stat.isDirectory()) {
                    // Skip node_modules and other common directories to avoid
                    if (!entry.startsWith('.') && entry !== 'node_modules') {
                        searchDirectory(fullPath);
                    }
                } else if (entry === 'package.json') {
                    files.push(fullPath);
                }
            }
        } catch (error) {
            // Skip directories we can't read
            console.warn(`Warning: Could not read directory ${currentDir}`);
        }
    }

    searchDirectory(dir);
    return files;
}

async function update() {
    const findFiles = findPackageJsonFiles();
    for (const file of findFiles) {
        console.log(file)
        const json = JSON.parse(readFileSync(file, {encoding: "utf8"}));
        if (json && json.dependencies && json.dependencies["@skywind-group/sw-currency-exchange"]) {
            json.dependencies["@skywind-group/sw-currency-exchange"] = process.env.VERSION;
            writeFileSync(file, JSON.stringify(json, null, 2));
        }
    }
}

void update()
