#!/usr/bin/env bash

branch_name="SWS-51581-fun-bonus-mode-please-change-server-s-currency-code-for-fun-bonus-mode-from-x15-to-bnc"
commit_message="SWS-51581: [FUN Bonus Mode] Please change server's currency code for FUN Bonus mode from X15 to BNC"

V1_VERSION="1.6.18"
V2_VERSION="2.3.18"
target_branch="release/5.55"

clean_up() {
  test -d "$tmp_dir" && rm -fr "$tmp_dir"
}

upgrade() {
  git clone --branch "${target_branch}" --depth 1 "${1}"
  cd "$(basename "$1" .git)" || exit

  git checkout -b "${branch_name}"
  VERSION="${2}" node "${dir}/update-version.js"
  git commit -a -m "${commit_message}"
  git push -u origin "${branch_name}"
  cd ".." || exit
}

upgrade_v1() {
  upgrade "$1" $V1_VERSION
}

upgrade_v2() {
  upgrade "$1" $V2_VERSION
}

tmp_dir=$( mktemp -d -t my-script )
trap "clean_up $tmp_dir" EXIT

dir="$(cd -P -- "$(dirname -- "${BASH_SOURCE[0]}")" && pwd -P)"
cd "${tmp_dir}" || exit

# upgrade_v2 "ssh://******************************:7999/sbep/sw-currency-exchange-api.git"
# upgrade_v2 "ssh://******************************:7999/sbep/sw-management-api.git"
# upgrade_v2 "ssh://******************************:7999/swb/ipm-mock.git"
# upgrade_v2 "ssh://******************************:7999/swb/sw-integration-seamless.git"
#upgrade_v2 "ssh://******************************:7999/swjpn/sw-jpn-server-api.git"
upgrade_v2 "ssh://******************************:7999/sbep/sw-slot-engine.git"

#upgrade_v1 "ssh://******************************:7999/swb/sw-integration-relax.git"
